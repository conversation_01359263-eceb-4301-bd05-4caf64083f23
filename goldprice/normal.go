package goldprice

import (
    "net/http"
    "github.com/labstack/echo/v4"
    "gorm.io/gorm"
)

// errorResponse helper
func errorResponse(c echo.Context, message string) error {
    return c.JSON(http.StatusBadRequest, map[string]interface{}{
        "error":   true,
        "message": message,
    })
}

// successResponse helper
func successResponse(c echo.Context, data interface{}) error {
    return c.JSON(http.StatusOK, map[string]interface{}{
        "error": false,
        "data":  data,
    })
}

func NormalGoldPriceHandler(db *gorm.DB) echo.HandlerFunc {
    return func(c echo.Context) error {
        tx := db.Begin()

        var goldPrices []struct {
            Purity          string  `json:"Purity"`
            HargaPelanggan  float64 `json:"Harga_Pelanggan"`
            HargaMember     float64 `json:"Harga_Member"`
            HargaPengedar   float64 `json:"Harga_Pengedar"`
            HargaRAF        float64 `json:"Harga_RAF"`
            HargaND         float64 `json:"harga_nd"`
            HargaTradeIn    float64 `json:"harga_trade_in"`
            HargaBuyback    float64 `json:"harga_buyback"`
        }

        err := tx.Table("hargaemas").
            Select("Purity, Harga_Pelanggan, Harga_Member, Harga_Pengedar, Harga_RAF, harga_nd, harga_trade_in, harga_buyback").
            Where("cawangan = ? AND is_publish = ?", "Online", 1).
            Order("CONVERT(Purity, SIGNED) DESC").
            Scan(&goldPrices).Error
        if err != nil {
            tx.Rollback()
            return errorResponse(c, err.Error())
        }

        var timeRecord struct {
            WriteTimestamp string `json:"write_timestamp"`
        }

        err = tx.Table("hargaemas").
            Select("write_timestamp").
            Where("cawangan = ? AND is_publish = ?", "Online", 1).
            Order("write_timestamp DESC").
            Limit(1).
            Scan(&timeRecord).Error
        if err != nil {
            tx.Rollback()
            return errorResponse(c, err.Error())
        }

        tx.Commit()

        data := map[string]interface{}{
            "gold_price": goldPrices,
            "time":       timeRecord,
        }
        return successResponse(c, data)
    }
}
