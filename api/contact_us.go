package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Models for contact_us response
type Address struct {
	Cawangan string `json:"cawangan"`
	Alamat   string `json:"alamat"`
	NoTel    string `json:"no_tel"`
}

type WorkingTime struct {
	// Define fields as per your ecomm_working_day_time table columns
	ID       uint   `json:"id"`
	Day      string `json:"day"`
	OpenTime string `json:"open_time"`
	CloseTime string `json:"close_time"`
	// Add more fields as needed
}

// ContactUsHandler returns contact us info
func ContactUsHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var addresses []Address
		var mapLink string
		var workingTimes []WorkingTime

		// Get addresses where ecomm_is_display = 1
		if err := tx.Table("56_maklumat_kedai").
			Where("ecomm_is_display = ?", 1).
			Select("cawangan, alamat, no_tel").
			Scan(&addresses).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Get the first gmaps_link from ecomm_settings
		if err := tx.Table("ecomm_settings").
			Select("gmaps_link").
			Limit(1).
			Scan(&mapLink).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Get all working times
		if err := tx.Table("ecomm_working_day_time").
			Find(&workingTimes).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, map[string]interface{}{
			"addresses":    addresses,
			"map_link":     mapLink,
			"working_time": workingTimes,
		})
	}
}
