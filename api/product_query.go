package api

import (
	"gorm.io/gorm"
)

type ProductQuery struct {
	DB *gorm.DB
}

func NewProductQuery(db *gorm.DB) *ProductQuery {
	return &ProductQuery{DB: db}
}

func (p *ProductQuery) SettingProductBranch() int {
	// Return 1 if Online branch filter should be applied. Replace with your actual config or DB lookup.
	return 1
}

func (p *ProductQuery) AhliPriceFeatured() *gorm.DB {
	query := p.DB.Table("data_database").
		Select([]string{
			"data_database.id",
			"data_database.receiving_Status",
			"data_database.cawangan",
			"data_database.dimension_Panjang",
			"data_database.dimension_Lebar",
			"data_database.dimension_Saiz",
			"data_database.kod_Purity",
			"data_database.kategori_Produk",
			"data_database.no_siri_Produk",
			"data_database.Berat",
			"data_database.Beza_Berat",
			"data_database.img_path",
			"data_database.img_path_2",
			"data_database.is_featured",
			"data_database.is_exclusive",
			"data_database.upah_g_comm_ahli AS workmanship",
			"data_database.nota_lain_lain",
			"data_database.design",
			"data_database.code_design",
			"data_database.StatusItem",
			"data_database.is_sale",
			"data_database.diskaun_ahli AS discount",
			"data_database.kadar_diskaun_ahli AS promosi",
			"design.image_1 AS image_1",
			"design.image_2 AS image_2",
			"design.id AS design_id",
			"hargaemas.Harga_Member AS gold_price",
		}).
		Where("data_database.StatusItem IN ?", []int{10, 51}).
		Where("data_database.combo_id IS NULL").
		Where("data_database.is_featured = ?", 1).
		Where("data_database.receiving_Status IS NOT NULL").
		Joins("JOIN hargaemas ON data_database.kod_Purity = hargaemas.Purity").
		Where("hargaemas.cawangan = ?", "Online").
		Joins("JOIN design ON data_database.code_design = design.design_code")

	query = query.Select(p.DB.Statement.Selects).
		Select(`
			CASE
				WHEN data_database.receiving_Status IN ('1', '3', '7', '10') 
					THEN data_database.HargaJualan_Member - data_database.diskaun_pelanggan_biasa
				WHEN data_database.receiving_Status IN ('0', '2', '4', '5', '6', '9') 
					THEN data_database.Beza_Berat * (hargaemas.Harga_Member - data_database.diskaun_ahli)
			END AS price,
			CASE
				WHEN data_database.receiving_Status IN ('1', '3', '7', '10') 
					THEN data_database.HargaJualan_Member
				WHEN data_database.receiving_Status IN ('0', '2', '4', '5', '6', '9') 
					THEN data_database.Beza_Berat * hargaemas.Harga_Member
			END AS old_price
		`)

	if p.SettingProductBranch() == 1 {
		query = query.Where("data_database.cawangan = ?", "Online")
	}

	return query
}
