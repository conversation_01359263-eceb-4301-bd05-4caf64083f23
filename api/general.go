package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Setting matches ecomm_settings table
type Setting struct {
	EcommName      string `json:"ecomm_name"`
	EcommTagline   string `json:"ecomm_tagline"`
	EcommLogo      string `json:"ecomm_logo"`
	EcommFavicon   string `json:"ecomm_favicon"`
	CompanyName    string `json:"company_name"`
	CompanyPhone   string `json:"company_phone"`
	CompanyEmail   string `json:"company_email"`
	CompanyAddress string `json:"company_address"`
	PromotionName  string `json:"promotion_name"`
	GmapsLink      string `json:"gmaps_link"`
}

// SocialMedia matches ecomm_social_medias table
type SocialMedia struct {
	ID        uint   `json:"id"`
	IconImage string `json:"icon_image"`
	Name      string `json:"name"`
	Link      string `json:"link"`
	Status    int    `json:"status"`
	CreatedAt *string `json:"created_at"`
	UpdatedAt *string `json:"updated_at"`
}

// SettingResponseData holds the response data structure for settings
type SettingResponseData struct {
	Setting      Setting       `json:"setting"`
	SocialMedias []SocialMedia `json:"social_medias"`
}



// SettingHandler returns settings + social media links
func SettingHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var setting Setting
		var socialMedias []SocialMedia

		if err := tx.Table("ecomm_settings").
			Select(
				"ecomm_name",
				"ecomm_tagline",
				"ecomm_logo",
				"ecomm_favicon",
				"company_name",
				"company_phone",
				"company_email",
				"company_address",
				"promotion_name",
				"gmaps_link",
			).
			Where("id = ?", 1).
			First(&setting).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		if err := tx.Table("ecomm_social_medias").
			Select("id, icon_image, name, link, status, created_at, updated_at").
			Where("status = ?", 1).
			Find(&socialMedias).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()

		resp := ApiResponse[SettingResponseData]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Desc:       "",
			Error:      false,
			Message:    "",
			Data: SettingResponseData{
				Setting:      setting,
				SocialMedias: socialMedias,
			},
		}

		return c.JSON(http.StatusOK, resp)
	}
}
