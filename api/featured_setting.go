package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

// FeatureSettingData holds the combined feature toggle settings
type FeatureSettingData struct {
	Merchandise   int `json:"merchandise"`
	Engrave       int `json:"engrave"`
	Combo         int `json:"combo"`
	Varian        int `json:"varian"`
	PreOrder      int `json:"pre_order"`
	Collection    int `json:"collection"`
	Point         int `json:"point"`
	Card          int `json:"card"`
	Brand         int `json:"brand"`
	Newsletter    int `json:"newsletter"`
	Testimoni     int `json:"testimoni"`
	Coupon        int `json:"coupon"`
	Affiliate     int `json:"affiliate"`
	GoldSaving    int `json:"gold_saving"`
	TrustedAgent  int `json:"trusted_agent"`
}



// FeatureSettingHandler handles /v1/feature-setting
func FeatureSettingHandler() echo.HandlerFunc {
	return func(c echo.Context) error {
		data := FeatureSettingData{
			Merchandise:   settingMerchandise(),
			Engrave:       settingEngrave(),
			Combo:         settingCombo(),
			Varian:        settingVarian(),
			PreOrder:      settingPreOrder(),
			Collection:    settingCollection(),
			Point:         settingPoint(),
			Card:          settingCard(),
			Brand:         settingBrand(),
			Newsletter:    settingNewsletter(),
			Testimoni:     settingTestimoni(),
			Coupon:        settingCoupon(),
			Affiliate:     settingAffiliate(),
			GoldSaving:    settingGoldSaving(),
			TrustedAgent:  settingTrustedAgent(),
		}

		return c.JSON(http.StatusOK, ApiResponse[FeatureSettingData]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Desc:       "",
			Error:      false,
			Message:    "",
			Data:       data,
		})
	}
}

func settingMerchandise() int   { return 0 }
func settingEngrave() int       { return 0 }
func settingCombo() int         { return 0 }
func settingVarian() int        { return 0 }
func settingPreOrder() int      { return 0 }
func settingCollection() int    { return 0 }
func settingPoint() int         { return 0 }
func settingCard() int          { return 1 }
func settingBrand() int         { return 1 }
func settingNewsletter() int    { return 1 }
func settingTestimoni() int     { return 1 }
func settingCoupon() int        { return 1 }
func settingAffiliate() int     { return 0 }
func settingGoldSaving() int    { return 0 }
func settingTrustedAgent() int  { return 0 }
