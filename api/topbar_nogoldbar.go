package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type Category struct {
	ID                uint   `json:"id" gorm:"column:id"`
	KategoriProduk    string `json:"Kategori_Produk" gorm:"column:<PERSON>gori_Produk"`
	KodKategoriProduk string `json:"kod_kategori_produk" gorm:"column:Kod_Kategori_Produk"`
	ImgPath           string `json:"img_path" gorm:"column:img_path"`
}

type TopBarNoGoldbarResponse struct {
	Category         []Category   `json:"category"`
	CustomPage       []CustomPage `json:"custom_page"`
	FeaturedCategory []Category   `json:"featured_category"`
	Branch           []Branch     `json:"branch"`
}

func TopBarNoGoldbarHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var category []Category
		var featuredCategory []Category
		var customPage []CustomPage
		var branch []Branch

		// Category (excluding goldbar)
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_category_menu = ? AND is_publish = ? AND is_goldbar != ? AND img_path IS NOT NULL",
				"", 1, 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&category).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    err.Error(),
			})
		}

		// Featured Category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_featured = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&featuredCategory).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    err.Error(),
			})
		}

		// Custom Page
		if err := tx.Table("ecomm_custom_page").
			Where("status = ?", 1).
			Select("id, name").
			Scan(&customPage).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    err.Error(),
			})
		}

		// Branch
		if err := tx.Table("56_maklumat_kedai").
			Where("status = ? AND ecomm_is_display = ?", 1, 1).
			Select("cawangan").
			Scan(&branch).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    err.Error(),
			})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, ApiResponse[TopBarNoGoldbarResponse]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Error:      false,
			Message:    "",
			Data: TopBarNoGoldbarResponse{
				Category:         category,
				CustomPage:       customPage,
				FeaturedCategory: featuredCategory,
				Branch:           branch,
			},
		})
	}
}
