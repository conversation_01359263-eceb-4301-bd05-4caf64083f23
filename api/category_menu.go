package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// CategoryMenuResult matches the selected fields in your query
type CategoryMenuResult struct {
	KategoriProduk  string `json:"Kategori_Produk"`
	KodKategoriProduk string `json:"Kod_Kategori_Produk"`
	ID              uint   `json:"id"`
	NamaSupplier    string `json:"nama_Supplier"`
	KodSupplier     string `json:"Kod_Supplier"`
	Purity          string `json:"Purity"`
	PurityID        string `json:"purity_ID"`
}

// CategoryMenuHandler returns goldbar categories grouped by purity
func CategoryMenuHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var goldbarCategories []CategoryMenuResult

		// err := tx.Table("setting_database").
		// 	Select("setting_database.Kategori_Produk, setting_database.Kod_Kategori_Produk, setting_database.id, data_database.nama_Supplier, data_database.Kod_Supplier, data_database.Purity, data_database.purity_ID").
		// 	Joins("JOIN data_database ON data_database.kategori_produk_ID = setting_database.id").
		// 	Where("setting_database.status = ? AND setting_database.is_goldbar = ? AND setting_database.is_category_menu = ?", 1, 1, 1).
		// 	Group("data_database.Purity").
		// 	Scan(&goldbarCategories).Error

		err := tx.Table("setting_database").
			Select("setting_database.Kategori_Produk, setting_database.Kod_Kategori_Produk, setting_database.id, data_database.nama_Supplier, data_database.Kod_Supplier, data_database.Purity, data_database.purity_ID").
			Joins("JOIN data_database ON data_database.kategori_produk_ID = setting_database.id").
			Where("setting_database.status = ? AND setting_database.is_goldbar = ? AND setting_database.is_category_menu = ?", 1, 1, 1).
			Group("data_database.Purity, setting_database.Kategori_Produk, setting_database.Kod_Kategori_Produk, setting_database.id, data_database.nama_Supplier, data_database.Kod_Supplier, data_database.purity_ID").
			Scan(&goldbarCategories).Error


		if err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, goldbarCategories)
	}
}
