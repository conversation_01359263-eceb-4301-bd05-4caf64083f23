package api

import (
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"net/http"
)

type FeaturedProductRequest struct {
	UserCategoryID int `json:"user_category_id" form:"user_category_id"`
}

func FeaturedProductHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		var req FeaturedProductRequest
		if err := c.Bind(&req); err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request format"})
		}

		// Set default user_category_id to 1 if not provided or is 0
		if req.UserCategoryID == 0 {
			req.UserCategoryID = 1
		}

		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		query := NewProductQuery(tx)
		var q *gorm.DB

		switch req.UserCategoryID {
		case 1:
			q = query.AhliPriceFeatured()
		case 2:
			q = query.AhliPriceFeatured()
		case 3:
			q = query.AhliPriceFeatured()
		case 4:
			q = query.AhliPriceFeatured()
		case 5:
			q = query.AhliPriceFeatured()
		default:
			tx.Rollback()
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid user_category_id"})
		}

		var products []map[string]interface{}
		if err := q.Find(&products).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()
		return c.JSON(http.StatusOK, map[string]interface{}{
			"status":      "Success",
			"status_code": 200,
			"desc":        "",
			"error":       false,
			"message":     "",
			"data":        products,
		})
	}
}
