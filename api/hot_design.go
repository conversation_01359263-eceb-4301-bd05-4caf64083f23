package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Design represents a hot-selling design structure
type Design struct {
	ID         uint   `json:"id"`
	DesignCode string `json:"design_code"`
	DesignName string `json:"design_name"`
	CategoryID uint   `json:"category_id"`
	Image1     string `json:"image_1"`
	Image2     string `json:"image_2"`
}

// HotDesignHandler handles the /hot-designs route
func HotDesignHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var designs []Design

		err := tx.Table("design").
			Select("id, design_code, design_name, category_id, image_1, image_2").
			Where("is_hot_selling = ? AND is_publish = ? AND is_deleted = ?", 1, 1, 0).
			Where("image_1 IS NOT NULL AND image_2 IS NOT NULL AND image_3 IS NOT NULL AND image_4 IS NOT NULL AND image_5 IS NOT NULL").
			Order("design_name ASC").
			Scan(&designs).Error

		if err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]interface{}{
				"status":      "Error",
				"status_code": 500,
				"desc":        "Database query error",
				"error":       true,
				"message":     err.Error(),
				"data":        nil,
			})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, map[string]interface{}{
			"status":      "Success",
			"status_code": 200,
			"desc":        "",
			"error":       false,
			"message":     "",
			"data":        designs,
		})
	}
}
