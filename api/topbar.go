package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Structs for your data
type TopBarCategory struct {
	ID               uint   `json:"id"`
	KategoriProduk   string `json:"Kategori_Produk"`
	KodKategoriProduk string `json:"Kod_Kategori_Produk"`
	ImgPath          string `json:"img_path"`
}

type CustomPage struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug,omitempty"` // slug can be empty
}

type Branch struct {
	Cawangan string `json:"cawangan"`
}

type TopBarResponseData struct {
	Category         []TopBarCategory `json:"category"`
	CustomPage       []CustomPage     `json:"custom_page"`
	FeaturedCategory []TopBarCategory `json:"featured_category"`
	Branch           []Branch         `json:"branch"`
}

// TopBarHandler handles /v1/top-bar route
func TopBarHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var category []TopBarCategory
		var featuredCategory []TopBarCategory
		var customPage []CustomPage
		var branch []Branch

		// Fetch category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_category_menu = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&category).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Desc:       "",
				Error:      true,
				Message:    err.Error(),
			})
		}

		// Fetch featured category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_featured = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&featuredCategory).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Desc:       "",
				Error:      true,
				Message:    err.Error(),
			})
		}

		// Check if slug column exists in ecomm_custom_page
		var slugColumn string
		err := tx.Raw(`SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'ecomm_custom_page' AND COLUMN_NAME = 'slug'`).Scan(&slugColumn).Error
		if err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Desc:       "",
				Error:      true,
				Message:    err.Error(),
			})
		}

		if slugColumn != "" {
			if err := tx.Table("ecomm_custom_page").
				Where("status = ?", 1).
				Select("id, name, slug").
				Scan(&customPage).Error; err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
					Status:     "Error",
					StatusCode: http.StatusInternalServerError,
					Desc:       "",
					Error:      true,
					Message:    err.Error(),
				})
			}
		} else {
			if err := tx.Table("ecomm_custom_page").
				Where("status = ?", 1).
				Select("id, name").
				Scan(&customPage).Error; err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
					Status:     "Error",
					StatusCode: http.StatusInternalServerError,
					Desc:       "",
					Error:      true,
					Message:    err.Error(),
				})
			}
		}

		// Fetch branch
		if err := tx.Table("56_maklumat_kedai").
			Where("status = ? AND ecomm_is_display = ?", 1, 1).
			Select("cawangan").
			Scan(&branch).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Desc:       "",
				Error:      true,
				Message:    err.Error(),
			})
		}

		tx.Commit()

		resp := ApiResponse[TopBarResponseData]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Desc:       "",
			Error:      false,
			Message:    "",
			Data: TopBarResponseData{
				Category:         category,
				CustomPage:       customPage,
				FeaturedCategory: featuredCategory,
				Branch:           branch,
			},
		}

		return c.JSON(http.StatusOK, resp)
	}
}
